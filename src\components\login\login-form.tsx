'use client'

import { cn } from "@/lib/utils"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { CardContent } from "@/components/ui/card"
import { GlassCard } from "@/components/ui/glass-card"
import { EnhancedInput } from "@/components/ui/enhanced-input"
import { Label } from "@/components/ui/label"
import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { toast } from "sonner"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { Mail, User, Lock, GitBranch } from "lucide-react"

const loginSchema = z.object({
  email: z.string().email("Please enter a valid email address"),
  password: z.string().min(1, "Password is required"),
})

type FormData = z.infer<typeof loginSchema>

export function LoginForm({
  className,
  ...props
}: React.ComponentProps<"div">) {
  const [isLoading, setIsLoading] = useState(false)
  const [socialLoading, setSocialLoading] = useState<string | null>(null)
  const router = useRouter()

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm<FormData>({
    resolver: zodResolver(loginSchema),
  })

  const onSubmit = async (data: FormData) => {
    setIsLoading(true)
    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          email: data.email,
          password: data.password,
        }),
      })

      const result = await response.json()

      if (!response.ok) {
        // Handle email verification requirement
        if (result.requiresEmailVerification) {
          toast.error('Please verify your email before logging in')
          router.push(`/verify-email?email=${encodeURIComponent(result.email)}`)
          return
        }
        throw new Error(result.error || 'Failed to login')
      }

      // Store token and user info
      localStorage.setItem('token', result.token)
      localStorage.setItem('userEmail', data.email)

      // Check if user completed questionnaire
      if (result.user && !result.user.hasCompletedQuestionnaire) {
        toast.success('Please complete your profile to continue')
        router.push('/questionnaire')
        return
      }

      // Determine user role and redirect
      if (result.user.role === 'ADMIN') {
        localStorage.setItem('userRole', 'admin')
        toast.success('Successfully logged in as Admin!')
        router.push('/dashboard/admin-dashboard')
      } else {
        localStorage.setItem('userRole', 'client')
        toast.success('Successfully logged in!')
        router.push('/dashboard/client-dashboard')
      }
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Failed to login')
    } finally {
      setIsLoading(false)
    }
  }

  const handleSocialLogin = async (provider: string) => {
    setSocialLoading(provider)
    try {
      // Implement social login here
      await new Promise(resolve => setTimeout(resolve, 1000)) // Simulated delay
      toast.error('Social login is not implemented yet')
    } finally {
      setSocialLoading(null)
    }
  }

  return (
    <div className={cn("flex flex-col gap-6", className)} {...props}>
      <GlassCard className="overflow-hidden border-border/30 shadow-2xl" blur="lg" opacity={0.9}>
        <CardContent className="grid p-0 md:grid-cols-2">
          <form onSubmit={handleSubmit(onSubmit)} className="p-8 md:p-10">
            <div className="flex flex-col gap-6">
              <div className="flex flex-col items-center text-center">
                <h1 className="text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-primary to-accent">Welcome back</h1>
                <p className="text-balance text-muted-foreground mt-2">
                  Login to your account
                </p>

              </div>

              <div className="grid gap-3">
                <Label htmlFor="email" className="text-sm font-medium">Email</Label>
                <EnhancedInput
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  disabled={isLoading}
                  {...register("email")}
                  icon={<User className="h-4 w-4 text-muted-foreground" />}
                  clearable
                  size="lg"
                  radius="lg"
                  variant="filled"
                  className={cn(
                    errors.email && "border-destructive focus-visible:ring-destructive"
                  )}
                  aria-describedby={errors.email ? "email-error" : undefined}
                />
                {errors.email && (
                  <p id="email-error" className="text-sm text-destructive">{errors.email.message}</p>
                )}
              </div>

              <div className="grid gap-3">
                <div className="flex items-center justify-between">
                  <Label htmlFor="password" className="text-sm font-medium">Password</Label>
                  <Link
                    href="/forgot-password"
                    className="text-sm text-primary underline-offset-4 hover:underline"
                    tabIndex={0}
                  >
                    Forgot password?
                  </Link>
                </div>
                <EnhancedInput
                  id="password"
                  type="password"
                  placeholder="Your password"
                  disabled={isLoading}
                  {...register("password")}
                  icon={<Lock className="h-4 w-4 text-muted-foreground" />}
                  showPasswordToggle
                  size="lg"
                  radius="lg"
                  variant="filled"
                  className={cn(
                    errors.password && "border-destructive focus-visible:ring-destructive"
                  )}
                  aria-describedby={errors.password ? "password-error" : undefined}
                />
                {errors.password && (
                  <p id="password-error" className="text-sm text-destructive">{errors.password.message}</p>
                )}
              </div>

              <Button
                type="submit"
                disabled={isLoading}
                size="lg"
                className="rounded-lg mt-2"
              >
                {isLoading ? "Signing in..." : "Login with Email"}
              </Button>

              <div className="relative text-center text-sm">
                <span className="bg-background px-3 text-muted-foreground relative z-10">
                  Or continue with
                </span>
                <div className="absolute inset-0 flex items-center">
                  <span className="w-full border-t" />
                </div>
              </div>

              <div className="grid gap-4">
                <Button
                  type="button"
                  variant="outline"
                  disabled={socialLoading === 'github'}
                  onClick={() => handleSocialLogin('github')}
                  className="h-11 rounded-lg"
                >
                  {socialLoading === 'github' ? (
                    "Connecting..."
                  ) : (
                    <>
                      <GitBranch className="mr-2 h-5 w-5" />
                      Login with GitHub
                    </>
                  )}
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  disabled={socialLoading === 'google'}
                  onClick={() => handleSocialLogin('google')}
                  className="h-11 rounded-lg"
                >
                  {socialLoading === 'google' ? (
                    "Connecting..."
                  ) : (
                    <>
                      <Mail className="mr-2 h-5 w-5" />
                      Login with Google
                    </>
                  )}
                </Button>
              </div>

              <p className="text-center text-sm text-muted-foreground">
                Don't have an account?{" "}
                <Link href="/signup" className="text-primary font-medium underline-offset-4 hover:underline">
                  Sign up
                </Link>
              </p>
            </div>
          </form>

          <div className="relative hidden md:block bg-gradient-to-br from-primary/5 to-accent/5">
            <div className="absolute inset-0 bg-gradient-to-tr from-primary/20 via-accent/10 to-background" />
            <div className="absolute top-1/4 left-1/4 w-1/2 h-1/2 rounded-full bg-primary/10 blur-3xl"></div>
            <div className="absolute inset-0 flex items-center justify-center p-10">
              <div className="bg-card/80 backdrop-blur-sm p-8 rounded-2xl shadow-lg border max-w-sm">
                <blockquote className="space-y-3">
                  <p className="text-lg font-medium">
                    &ldquo;Welcome back! We've been working on some exciting new features for you.&rdquo;
                  </p>
                  <footer className="text-sm text-muted-foreground">
                    — The Your Company Team
                  </footer>
                </blockquote>
              </div>
            </div>
          </div>
        </CardContent>
      </GlassCard>
    </div>
  )
}
