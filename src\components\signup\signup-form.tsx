'use client'

import * as React from "react"
import { cn } from "@/lib/utils"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { GlassCard } from "@/components/ui/glass-card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { toast } from "sonner"
import Link from "next/link"
import { Github, Mail, AlertCircle } from "lucide-react"
import { useRouter } from "next/navigation"

const formSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  email: z.string().email("Please enter a valid email address"),
  password: z.string()
    .min(8, "Password must be at least 8 characters")
    .regex(/[A-Z]/, "Password must contain at least one uppercase letter")
    .regex(/[a-z]/, "Password must contain at least one lowercase letter")
    .regex(/[0-9]/, "Password must contain at least one number")
    .regex(/[!@#$%^&*(),.?":{}|<>]/, "Password must contain at least one special character"),
  confirmPassword: z.string()
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
})

type FormData = z.infer<typeof formSchema>

export function SignupForm({
  className,
  ...props
}: React.ComponentProps<"div">) {
  const [isLoading, setIsLoading] = React.useState(false)
  const [socialLoading, setSocialLoading] = React.useState<string | null>(null)
  const [passwordStrength, setPasswordStrength] = React.useState(0)
  const router = useRouter()

  const {
    register,
    handleSubmit,
    watch,
    formState: { errors, isSubmitting },
  } = useForm<FormData>({
    resolver: zodResolver(formSchema),
  })

  // Watch password field for strength indicator
  React.useEffect(() => {
    const subscription = watch((value, { name }) => {
      if (name === 'password') {
        const password = value.password as string || ''
        let strength = 0
        if (password.length >= 8) strength += 1
        if (/[A-Z]/.test(password)) strength += 1
        if (/[a-z]/.test(password)) strength += 1
        if (/[0-9]/.test(password)) strength += 1
        if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) strength += 1
        setPasswordStrength(strength)
      }
    })
    return () => subscription.unsubscribe()
  }, [watch])

  const onSubmit = async (data: FormData) => {
    setIsLoading(true)
    try {
      const response = await fetch('/api/auth/signup', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          name: data.name,
          email: data.email,
          password: data.password,
        }),
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Something went wrong')
      }

      // Store token for questionnaire
      if (result.token) {
        localStorage.setItem('token', result.token)
        localStorage.setItem('userEmail', data.email)
      }

      toast.success('Account created successfully! Please verify your email.')

      // Show verification code in development
      if (result.debug?.verificationCode) {
        toast.info(`Verification code: ${result.debug.verificationCode}`)
      }

      router.push(`/verify-email?email=${encodeURIComponent(data.email)}`)
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Failed to create account')
    } finally {
      setIsLoading(false)
    }
  }

  const handleSocialSignup = async (provider: 'github' | 'google') => {
    setSocialLoading(provider)
    try {
      const { initiateSocialLogin } = await import('@/lib/social-auth')
      await initiateSocialLogin(provider)
    } catch (error) {
      toast.error(error instanceof Error ? error.message : `${provider} signup failed`)
      setSocialLoading(null)
    }
  }

  return (
    <div className={cn("flex flex-col gap-6", className)} {...props}>
      <GlassCard className="overflow-hidden border-border/30 shadow-2xl" blur="lg" opacity={0.9}>
        <CardContent className="grid p-0 md:grid-cols-2">
          <form onSubmit={handleSubmit(onSubmit)} className="p-6 md:p-8">
            <div className="flex flex-col gap-6">
              <div className="flex flex-col items-center text-center">
                <h1 className="text-2xl font-bold">Create an account</h1>
                <p className="text-balance text-muted-foreground">
                  Enter your details below to create your account
                </p>
              </div>

              <div className="grid gap-2">
                <Label htmlFor="name">Full Name</Label>
                <Input
                  id="name"
                  type="text"
                  disabled={isLoading}
                  {...register("name")}
                  className={cn(errors.name && "border-destructive focus-visible:ring-destructive")}
                  aria-describedby={errors.name ? "name-error" : undefined}
                />
                {errors.name && (
                  <p id="name-error" className="text-sm text-destructive flex items-center gap-2">
                    <AlertCircle className="h-4 w-4" />
                    {errors.name.message}
                  </p>
                )}
              </div>

              <div className="grid gap-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  disabled={isLoading}
                  {...register("email")}
                  className={cn(errors.email && "border-destructive focus-visible:ring-destructive")}
                  aria-describedby={errors.email ? "email-error" : undefined}
                />
                {errors.email && (
                  <p id="email-error" className="text-sm text-destructive flex items-center gap-2">
                    <AlertCircle className="h-4 w-4" />
                    {errors.email.message}
                  </p>
                )}
              </div>

              <div className="grid gap-2">
                <Label htmlFor="password">Password</Label>
                <Input
                  id="password"
                  type="password"
                  disabled={isLoading}
                  {...register("password")}
                  className={cn(errors.password && "border-destructive focus-visible:ring-destructive")}
                  aria-describedby={errors.password ? "password-error" : undefined}
                />
                {errors.password ? (
                  <p id="password-error" className="text-sm text-destructive flex items-center gap-2">
                    <AlertCircle className="h-4 w-4" />
                    {errors.password.message}
                  </p>
                ) : (
                  <div className="space-y-2">
                    <div className="flex gap-2">
                      {Array.from({ length: 5 }).map((_, i) => (
                        <div
                          key={i}
                          className={cn(
                            "h-1 w-full rounded-full transition-colors",
                            i < passwordStrength
                              ? passwordStrength <= 2
                                ? "bg-red-500"
                                : passwordStrength <= 3
                                ? "bg-yellow-500"
                                : "bg-green-500"
                              : "bg-gray-200"
                          )}
                        />
                      ))}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      Password strength: {['Too weak', 'Weak', 'Fair', 'Good', 'Strong'][passwordStrength] || 'Too weak'}
                    </div>
                  </div>
                )}
              </div>

              <div className="grid gap-2">
                <Label htmlFor="confirmPassword">Confirm Password</Label>
                <Input
                  id="confirmPassword"
                  type="password"
                  disabled={isLoading}
                  {...register("confirmPassword")}
                  className={cn(errors.confirmPassword && "border-destructive focus-visible:ring-destructive")}
                  aria-describedby={errors.confirmPassword ? "confirm-password-error" : undefined}
                />
                {errors.confirmPassword && (
                  <p id="confirm-password-error" className="text-sm text-destructive flex items-center gap-2">
                    <AlertCircle className="h-4 w-4" />
                    {errors.confirmPassword.message}
                  </p>
                )}
              </div>

              <Button type="submit" disabled={isLoading}>
                {isLoading ? "Creating Account..." : "Create Account"}
              </Button>

              <div className="relative text-center text-sm">
                <span className="bg-background px-2 text-muted-foreground relative z-10">
                  Or continue with
                </span>
                <div className="absolute inset-0 flex items-center">
                  <span className="w-full border-t" />
                </div>
              </div>

              <div className="grid gap-4">
                <Button
                  type="button"
                  variant="outline"
                  disabled={socialLoading === 'github'}
                  onClick={() => handleSocialSignup('github')}
                >
                  {socialLoading === 'github' ? (
                    "Connecting..."
                  ) : (
                    <>
                      <Github className="mr-2 h-4 w-4" />
                      Sign up with GitHub
                    </>
                  )}
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  disabled={socialLoading === 'google'}
                  onClick={() => handleSocialSignup('google')}
                >
                  {socialLoading === 'google' ? (
                    "Connecting..."
                  ) : (
                    <>
                      <Mail className="mr-2 h-4 w-4" />
                      Sign up with Google
                    </>
                  )}
                </Button>
              </div>

              <p className="text-center text-sm text-muted-foreground">
                Already have an account?{" "}
                <Link href="/login" className="text-primary underline-offset-4 hover:underline">
                  Log in
                </Link>
              </p>
            </div>
          </form>

          <div className="relative hidden md:block bg-muted">
            <div className="absolute inset-0 bg-gradient-to-tr from-primary/20 via-primary/10 to-background" />
            <div className="absolute inset-0 flex items-center justify-center p-8">
              <blockquote className="space-y-2">
                <p className="text-lg">
                  &ldquo;Join our community of innovators and build the future together.&rdquo;
                </p>
              </blockquote>
            </div>
          </div>
        </CardContent>
      </GlassCard>

      <div className="text-balance text-center text-xs text-muted-foreground [&_a]:underline [&_a]:underline-offset-4 hover:[&_a]:text-primary">
        By signing up, you agree to our{" "}
        <Link href="/terms">Terms of Service</Link> and{" "}
        <Link href="/privacy">Privacy Policy</Link>.
      </div>
    </div>
  )
}
