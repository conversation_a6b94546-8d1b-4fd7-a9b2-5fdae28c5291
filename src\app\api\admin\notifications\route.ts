import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { verifyToken } from '@/lib/auth'

export async function GET(request: Request) {
  try {
    // Get token from Authorization header
    const authHeader = request.headers.get('authorization')
    const token = authHeader?.replace('Bearer ', '')

    if (!token) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Verify token and get user
    const user = await verifyToken(token)
    if (!user) {
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 401 }
      )
    }

    // Check if user is admin
    const adminUser = await prisma.user.findUnique({
      where: { id: user.id },
      select: { role: true }
    })

    if (adminUser?.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      )
    }

    // Get recent registrations (last 30 days)
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
    
    const recentRegistrations = await prisma.user.findMany({
      where: {
        createdAt: {
          gte: thirtyDaysAgo
        },
        role: 'CLIENT'
      },
      select: {
        id: true,
        name: true,
        email: true,
        createdAt: true,
        hasCompletedQuestionnaire: true,
        servicesInterested: true,
        projectSpecifications: true,
        projects: {
          select: {
            id: true,
            name: true,
            status: true,
            createdAt: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    // Get unread notifications for admin
    const notifications = await prisma.notification.findMany({
      where: {
        userId: user.id,
        isRead: false
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    return NextResponse.json({
      recentRegistrations,
      notifications,
      totalNewClients: recentRegistrations.length
    })
  } catch (error) {
    console.error('Admin notifications error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Mark notification as read
export async function PATCH(request: Request) {
  try {
    const { notificationId } = await request.json()
    
    // Get token from Authorization header
    const authHeader = request.headers.get('authorization')
    const token = authHeader?.replace('Bearer ', '')

    if (!token) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Verify token and get user
    const user = await verifyToken(token)
    if (!user) {
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 401 }
      )
    }

    // Update notification
    await prisma.notification.update({
      where: {
        id: notificationId,
        userId: user.id
      },
      data: {
        isRead: true
      }
    })

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Mark notification read error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
