'use client'

import * as React from "react"
import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Checkbox } from "@/components/ui/checkbox"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { toast } from "sonner"
import { useRouter } from "next/navigation"
import { motion } from "framer-motion"
import {
  Code,
  Smartphone,
  Brain,
  Users,
  ArrowRight,
  CheckCircle,
  Lightbulb,
  Target
} from "lucide-react"

const questionnaireSchema = z.object({
  servicesInterested: z.array(z.string()).min(1, "Please select at least one service"),
  projectSpecifications: z.string().min(10, "Please provide more details about your project (minimum 10 characters)"),
  hasExistingBrand: z.boolean().optional(),
  timeline: z.string().optional(),
  budget: z.string().optional(),
})

type FormData = z.infer<typeof questionnaireSchema>

const services = [
  {
    id: 'web_development',
    label: 'Web Development',
    description: 'Custom websites, web applications, and e-commerce solutions',
    icon: Code,
    color: 'text-blue-500'
  },
  {
    id: 'app_development',
    label: 'Mobile App Development',
    description: 'iOS and Android applications with modern features',
    icon: Smartphone,
    color: 'text-green-500'
  },
  {
    id: 'ai_integration',
    label: 'AI Integration Services',
    description: 'Integrate AI and machine learning into your projects',
    icon: Brain,
    color: 'text-purple-500'
  },
  {
    id: 'collaboration',
    label: 'Project Collaboration',
    description: 'Join our active projects and collaborate with our team',
    icon: Users,
    color: 'text-orange-500'
  }
]

const projectTypes = [
  "E-commerce Platform",
  "Portfolio Website",
  "Corporate Website",
  "Mobile Application",
  "AI-Powered Solution",
  "Custom Software",
  "Other"
]

export function QuestionnaireForm({
  className,
  ...props
}: React.ComponentProps<"div">) {
  const [isLoading, setIsLoading] = React.useState(false)
  const [currentStep, setCurrentStep] = React.useState(1)
  const router = useRouter()

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors, isSubmitting },
  } = useForm<FormData>({
    resolver: zodResolver(questionnaireSchema),
    defaultValues: {
      servicesInterested: [],
      projectSpecifications: "",
    }
  })

  const watchedServices = watch("servicesInterested")

  const handleServiceToggle = (serviceId: string) => {
    const currentServices = watchedServices || []
    const updatedServices = currentServices.includes(serviceId)
      ? currentServices.filter(id => id !== serviceId)
      : [...currentServices, serviceId]

    setValue("servicesInterested", updatedServices)
  }

  const onSubmit = async (data: FormData) => {
    setIsLoading(true)
    try {
      const token = localStorage.getItem('token')

      const response = await fetch('/api/auth/questionnaire', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          ...data,
          token // Fallback for development
        }),
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Something went wrong')
      }

      toast.success('Profile completed successfully!')
      router.push('/dashboard/client-dashboard')
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Failed to complete profile')
    } finally {
      setIsLoading(false)
    }
  }

  const nextStep = () => {
    if (currentStep < 3) {
      setCurrentStep(currentStep + 1)
    }
  }

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  return (
    <div className={cn("w-full max-w-2xl mx-auto", className)} {...props}>
      <Card className="backdrop-blur-md bg-background/80 border-border/50 shadow-2xl">
        <CardHeader className="text-center space-y-4">
          <motion.div
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.5 }}
          >
            <Target className="w-12 h-12 mx-auto text-primary mb-4" />
            <CardTitle className="text-2xl font-bold">Complete Your Profile</CardTitle>
            <CardDescription className="text-base">
              Help us understand your needs so we can create the perfect dashboard for you
            </CardDescription>
          </motion.div>

          {/* Progress indicator */}
          <div className="flex justify-center space-x-2">
            {[1, 2, 3].map((step) => (
              <div
                key={step}
                className={cn(
                  "w-3 h-3 rounded-full transition-colors",
                  step <= currentStep ? "bg-primary" : "bg-muted"
                )}
              />
            ))}
          </div>
        </CardHeader>

        <CardContent>
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            {/* Step 1: Services */}
            {currentStep === 1 && (
              <motion.div
                initial={{ x: 20, opacity: 0 }}
                animate={{ x: 0, opacity: 1 }}
                exit={{ x: -20, opacity: 0 }}
                className="space-y-6"
              >
                <div className="space-y-4">
                  <Label className="text-lg font-semibold flex items-center gap-2">
                    <Lightbulb className="w-5 h-5 text-primary" />
                    What services are you interested in?
                  </Label>
                  <p className="text-sm text-muted-foreground">
                    Select all that apply. You can always add more services later.
                  </p>

                  <div className="grid gap-4">
                    {services.map((service) => {
                      const Icon = service.icon
                      const isSelected = watchedServices?.includes(service.id)

                      return (
                        <motion.div
                          key={service.id}
                          whileHover={{ scale: 1.02 }}
                          whileTap={{ scale: 0.98 }}
                        >
                          <Card
                            className={cn(
                              "cursor-pointer transition-all duration-200 hover:shadow-md",
                              isSelected
                                ? "ring-2 ring-primary bg-primary/5"
                                : "hover:bg-muted/50"
                            )}
                            onClick={() => handleServiceToggle(service.id)}
                          >
                            <CardContent className="p-4">
                              <div className="flex items-start space-x-4">
                                <div className={cn("p-2 rounded-lg bg-background", service.color)}>
                                  <Icon className="w-6 h-6" />
                                </div>
                                <div className="flex-1">
                                  <div className="flex items-center justify-between">
                                    <h3 className="font-semibold">{service.label}</h3>
                                    {isSelected && (
                                      <CheckCircle className="w-5 h-5 text-primary" />
                                    )}
                                  </div>
                                  <p className="text-sm text-muted-foreground mt-1">
                                    {service.description}
                                  </p>
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                        </motion.div>
                      )
                    })}
                  </div>

                  {errors.servicesInterested && (
                    <p className="text-sm text-destructive">
                      {errors.servicesInterested.message}
                    </p>
                  )}
                </div>

                <div className="flex justify-end">
                  <Button
                    type="button"
                    onClick={nextStep}
                    disabled={!watchedServices?.length}
                    className="min-w-32"
                  >
                    Next Step
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Button>
                </div>
              </motion.div>
            )}

            {/* Step 2: Project Details */}
            {currentStep === 2 && (
              <motion.div
                initial={{ x: 20, opacity: 0 }}
                animate={{ x: 0, opacity: 1 }}
                exit={{ x: -20, opacity: 0 }}
                className="space-y-6"
              >
                <div className="space-y-4">
                  <Label htmlFor="projectSpecifications" className="text-lg font-semibold">
                    Tell us about your project
                  </Label>
                  <p className="text-sm text-muted-foreground">
                    Describe your project requirements, goals, and any specific features you need.
                  </p>

                  <Textarea
                    id="projectSpecifications"
                    placeholder="Example: I need an e-commerce website for selling handmade jewelry. It should have a product catalog, shopping cart, payment integration, and customer reviews. I'd like a modern, mobile-friendly design with easy navigation..."
                    className="min-h-32 resize-none"
                    {...register("projectSpecifications")}
                  />

                  {errors.projectSpecifications && (
                    <p className="text-sm text-destructive">
                      {errors.projectSpecifications.message}
                    </p>
                  )}
                </div>

                <div className="flex justify-between">
                  <Button type="button" variant="outline" onClick={prevStep}>
                    Previous
                  </Button>
                  <Button type="button" onClick={nextStep} className="min-w-32">
                    Next Step
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Button>
                </div>
              </motion.div>
            )}

            {/* Step 3: Additional Info */}
            {currentStep === 3 && (
              <motion.div
                initial={{ x: 20, opacity: 0 }}
                animate={{ x: 0, opacity: 1 }}
                exit={{ x: -20, opacity: 0 }}
                className="space-y-6"
              >
                <div className="space-y-4">
                  <Label className="text-lg font-semibold">
                    Additional Information (Optional)
                  </Label>

                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="timeline">Preferred Timeline</Label>
                      <Input
                        id="timeline"
                        placeholder="e.g., 2-3 months, ASAP, Flexible"
                        {...register("timeline")}
                      />
                    </div>

                    <div>
                      <Label htmlFor="budget">Budget Range</Label>
                      <Input
                        id="budget"
                        placeholder="e.g., $5,000 - $10,000, Open to discussion"
                        {...register("budget")}
                      />
                    </div>
                  </div>
                </div>

                <div className="flex justify-between">
                  <Button type="button" variant="outline" onClick={prevStep}>
                    Previous
                  </Button>
                  <Button
                    type="submit"
                    isLoading={isLoading}
                    className="min-w-32"
                  >
                    Complete Profile
                  </Button>
                </div>
              </motion.div>
            )}
          </form>
        </CardContent>
      </Card>
    </div>
  )
}
