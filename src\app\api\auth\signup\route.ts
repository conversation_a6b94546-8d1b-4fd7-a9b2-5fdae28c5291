import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { hashPassword, generateToken } from '@/lib/auth'
import { z } from 'zod'

const signupSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
  name: z.string().min(2, 'Name must be at least 2 characters').optional(),
})

export async function POST(request: Request) {
  try {
    const body = await request.json()
    const validatedData = signupSchema.parse(body)

    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email: validatedData.email }
    })

    if (existingUser) {
      return NextResponse.json(
        { error: 'User already exists' },
        { status: 400 }
      )
    }

    // Generate email verification code
    const verificationCode = Math.floor(100000 + Math.random() * 900000).toString()

    // Create new user
    const hashedPassword = await hashPassword(validatedData.password)
    const user = await prisma.user.create({
      data: {
        email: validatedData.email,
        hashedPassword,
        name: validatedData.name,
        emailVerificationToken: verificationCode,
        emailVerificationExpiry: new Date(Date.now() + 10 * 60 * 1000), // 10 minutes
      },
      select: {
        id: true,
        email: true,
        name: true,
        emailVerified: true,
      },
    })

    // Generate JWT token
    const token = generateToken(user)

    // TODO: In a real application, send verification email here
    // For development, we'll return the code in the response
    return NextResponse.json({
      user,
      token,
      // Remove this in production:
      debug: {
        verificationCode
      }
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.issues[0].message },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}