// Social authentication utilities
export interface SocialAuthProvider {
  name: string
  clientId: string
  redirectUri: string
  scope: string[]
  authUrl: string
}

// GitHub OAuth configuration
export const githubAuth: SocialAuthProvider = {
  name: 'github',
  clientId: process.env.NEXT_PUBLIC_GITHUB_CLIENT_ID || '',
  redirectUri: `${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/api/auth/callback/github`,
  scope: ['user:email', 'read:user'],
  authUrl: 'https://github.com/login/oauth/authorize'
}

// Google OAuth configuration
export const googleAuth: SocialAuthProvider = {
  name: 'google',
  clientId: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID || '',
  redirectUri: `${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/api/auth/callback/google`,
  scope: ['openid', 'email', 'profile'],
  authUrl: 'https://accounts.google.com/oauth2/auth'
}

// Generate OAuth URL
export function generateOAuthUrl(provider: SocialAuthProvider, state?: string): string {
  const params = new URLSearchParams({
    client_id: provider.clientId,
    redirect_uri: provider.redirectUri,
    scope: provider.scope.join(' '),
    response_type: 'code',
    ...(state && { state })
  })

  return `${provider.authUrl}?${params.toString()}`
}

// Generate random state for CSRF protection
export function generateState(): string {
  return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15)
}

// Social login handler
export async function initiateSocialLogin(providerName: 'github' | 'google'): Promise<void> {
  const provider = providerName === 'github' ? githubAuth : googleAuth
  
  if (!provider.clientId) {
    throw new Error(`${providerName} OAuth is not configured. Please add the client ID to environment variables.`)
  }

  // Generate and store state for CSRF protection
  const state = generateState()
  sessionStorage.setItem(`oauth_state_${providerName}`, state)
  
  // Generate OAuth URL and redirect
  const authUrl = generateOAuthUrl(provider, state)
  window.location.href = authUrl
}

// Verify OAuth state
export function verifyOAuthState(providerName: string, receivedState: string): boolean {
  const storedState = sessionStorage.getItem(`oauth_state_${providerName}`)
  sessionStorage.removeItem(`oauth_state_${providerName}`)
  return storedState === receivedState
}
