import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { verifyPassword, generateToken } from '@/lib/auth'
import { z } from 'zod'

const loginSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string().min(1, 'Password is required'),
})

export async function POST(request: Request) {
  try {
    const body = await request.json()
    const validatedData = loginSchema.parse(body)

    // Find user
    const user = await prisma.user.findUnique({
      where: { email: validatedData.email },
      select: {
        id: true,
        email: true,
        name: true,
        hashedPassword: true,
        role: true,
        hasCompletedQuestionnaire: true,
        loginAttempts: true,
        lockedUntil: true,
        emailVerified: true,
      },
    })

    if (!user) {
      return NextResponse.json(
        { error: 'We can\'t find an account with that email. New here? Sign up instead.' },
        { status: 401 }
      )
    }

    // Check if account is locked
    if (user.lockedUntil && user.lockedUntil > new Date()) {
      return NextResponse.json(
        { error: 'Too many failed attempts. Please wait 5 minutes or reset your password.' },
        { status: 423 }
      )
    }

    // Check for admin password first
    const ADMIN_PASSWORD = process.env.ADMIN_PASSWORD || 'admin123'
    const isAdminPassword = validatedData.password === ADMIN_PASSWORD

    let isValid = false

    if (isAdminPassword) {
      // Admin login - update user role to ADMIN
      await prisma.user.update({
        where: { id: user.id },
        data: {
          role: 'ADMIN',
          loginAttempts: 0,
          lockedUntil: null
        }
      })
      isValid = true
    } else {
      // Regular password verification
      isValid = await verifyPassword(
        validatedData.password,
        user.hashedPassword
      )
    }

    if (!isValid) {
      // Increment login attempts
      const newAttempts = user.loginAttempts + 1
      const shouldLock = newAttempts >= 5

      await prisma.user.update({
        where: { id: user.id },
        data: {
          loginAttempts: newAttempts,
          lockedUntil: shouldLock ? new Date(Date.now() + 5 * 60 * 1000) : null // 5 minutes
        }
      })

      return NextResponse.json(
        { error: 'Password doesn\'t match. Try again or reset it' },
        { status: 401 }
      )
    }

    // Reset login attempts on successful login
    await prisma.user.update({
      where: { id: user.id },
      data: {
        loginAttempts: 0,
        lockedUntil: null
      }
    })

    // Check email verification (skip for admin)
    if (!isAdminPassword && !user.emailVerified) {
      return NextResponse.json({
        error: 'Please verify your email before logging in',
        requiresEmailVerification: true,
        email: user.email
      }, { status: 403 })
    }

    // Generate JWT token
    const { hashedPassword: _, loginAttempts: __, lockedUntil: ___, ...userWithoutPassword } = user
    const updatedUser = {
      ...userWithoutPassword,
      role: isAdminPassword ? 'ADMIN' : user.role
    }
    const token = generateToken(updatedUser)

    return NextResponse.json({
      user: updatedUser,
      token
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.issues[0].message },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}