'use client'

import * as React from "react"
import { motion } from "framer-motion"
import { cn } from "@/lib/utils"

export interface AnimatedBackgroundProps {
  variant?: 'tech' | 'minimal' | 'particles' | 'waves'
  className?: string
}

const TechBackground = () => (
  <>
    {/* Grid pattern */}
    <div className="absolute inset-0 opacity-20">
      <div className="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.1)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.1)_1px,transparent_1px)] bg-[size:50px_50px]" />
    </div>
    
    {/* Floating geometric shapes */}
    <motion.div
      className="absolute top-20 left-20 w-32 h-32 border border-primary/30 rounded-lg"
      animate={{
        rotate: [0, 360],
        scale: [1, 1.1, 1],
      }}
      transition={{
        duration: 20,
        repeat: Infinity,
        ease: "linear"
      }}
    />
    
    <motion.div
      className="absolute top-40 right-32 w-24 h-24 border border-accent/30 rounded-full"
      animate={{
        rotate: [360, 0],
        scale: [1, 0.8, 1],
      }}
      transition={{
        duration: 15,
        repeat: Infinity,
        ease: "linear"
      }}
    />
    
    <motion.div
      className="absolute bottom-32 left-1/3 w-20 h-20 bg-primary/10 rounded-lg"
      animate={{
        y: [0, -20, 0],
        rotate: [0, 45, 0],
      }}
      transition={{
        duration: 8,
        repeat: Infinity,
        ease: "easeInOut"
      }}
    />
    
    {/* Code-like elements */}
    <div className="absolute top-1/4 right-20 text-primary/20 font-mono text-sm">
      <motion.div
        animate={{ opacity: [0.2, 0.6, 0.2] }}
        transition={{ duration: 3, repeat: Infinity }}
      >
        {'<div className="future">'}
      </motion.div>
      <motion.div
        animate={{ opacity: [0.2, 0.6, 0.2] }}
        transition={{ duration: 3, repeat: Infinity, delay: 0.5 }}
      >
        {'  <innovation />'}
      </motion.div>
      <motion.div
        animate={{ opacity: [0.2, 0.6, 0.2] }}
        transition={{ duration: 3, repeat: Infinity, delay: 1 }}
      >
        {'</div>'}
      </motion.div>
    </div>
  </>
)

const MinimalBackground = () => (
  <>
    {/* Gradient orbs */}
    <motion.div
      className="absolute top-20 left-20 w-64 h-64 rounded-full bg-primary/20 blur-3xl"
      animate={{
        x: [0, 100, 0],
        y: [0, -50, 0],
        scale: [1, 1.2, 1],
      }}
      transition={{
        duration: 8,
        repeat: Infinity,
        ease: "easeInOut"
      }}
    />
    
    <motion.div
      className="absolute top-40 right-20 w-48 h-48 rounded-full bg-accent/20 blur-3xl"
      animate={{
        x: [0, -80, 0],
        y: [0, 60, 0],
        scale: [1, 0.8, 1],
      }}
      transition={{
        duration: 10,
        repeat: Infinity,
        ease: "easeInOut",
        delay: 2
      }}
    />
    
    <motion.div
      className="absolute bottom-20 left-1/3 w-56 h-56 rounded-full bg-purple-500/15 blur-3xl"
      animate={{
        x: [0, 60, 0],
        y: [0, -40, 0],
        scale: [1, 1.1, 1],
      }}
      transition={{
        duration: 12,
        repeat: Infinity,
        ease: "easeInOut",
        delay: 4
      }}
    />
  </>
)

const ParticlesBackground = () => (
  <>
    {Array.from({ length: 30 }).map((_, i) => (
      <motion.div
        key={i}
        className="absolute w-1 h-1 bg-primary/40 rounded-full"
        style={{
          left: `${Math.random() * 100}%`,
          top: `${Math.random() * 100}%`,
        }}
        animate={{
          y: [0, -100, 0],
          opacity: [0, 1, 0],
          scale: [0, 1, 0],
        }}
        transition={{
          duration: 3 + Math.random() * 2,
          repeat: Infinity,
          delay: Math.random() * 5,
          ease: "easeInOut"
        }}
      />
    ))}
    
    {Array.from({ length: 20 }).map((_, i) => (
      <motion.div
        key={`large-${i}`}
        className="absolute w-2 h-2 bg-accent/30 rounded-full"
        style={{
          left: `${Math.random() * 100}%`,
          top: `${Math.random() * 100}%`,
        }}
        animate={{
          x: [0, Math.random() * 200 - 100, 0],
          y: [0, Math.random() * 200 - 100, 0],
          opacity: [0.3, 0.8, 0.3],
        }}
        transition={{
          duration: 5 + Math.random() * 3,
          repeat: Infinity,
          delay: Math.random() * 3,
          ease: "easeInOut"
        }}
      />
    ))}
  </>
)

const WavesBackground = () => (
  <>
    <motion.div
      className="absolute inset-0 opacity-30"
      style={{
        background: `
          radial-gradient(circle at 20% 50%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
          radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
          radial-gradient(circle at 40% 80%, rgba(120, 219, 255, 0.3) 0%, transparent 50%)
        `
      }}
      animate={{
        backgroundPosition: ['0% 0%', '100% 100%', '0% 0%'],
      }}
      transition={{
        duration: 20,
        repeat: Infinity,
        ease: "linear"
      }}
    />
    
    {/* Floating waves */}
    <motion.div
      className="absolute bottom-0 left-0 w-full h-32 opacity-20"
      style={{
        background: 'linear-gradient(45deg, transparent, rgba(120, 119, 198, 0.5), transparent)'
      }}
      animate={{
        x: ['-100%', '100%'],
      }}
      transition={{
        duration: 15,
        repeat: Infinity,
        ease: "linear"
      }}
    />
  </>
)

export function AnimatedBackground({ 
  variant = 'tech', 
  className 
}: AnimatedBackgroundProps) {
  const renderBackground = () => {
    switch (variant) {
      case 'tech':
        return <TechBackground />
      case 'minimal':
        return <MinimalBackground />
      case 'particles':
        return <ParticlesBackground />
      case 'waves':
        return <WavesBackground />
      default:
        return <TechBackground />
    }
  }

  return (
    <div className={cn("absolute inset-0 -z-10 overflow-hidden", className)}>
      {/* Base gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-primary/10 via-background to-accent/10" />
      
      {/* Variant-specific background */}
      {renderBackground()}
    </div>
  )
}
