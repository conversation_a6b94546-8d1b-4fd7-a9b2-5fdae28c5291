'use client'

import * as React from "react"
import { cn } from "@/lib/utils"
import { AlertCircle, Info, CheckCircle, XCircle } from "lucide-react"
import { motion, AnimatePresence } from "framer-motion"

export interface FormErrorProps {
  type?: 'error' | 'warning' | 'info' | 'success'
  message: string
  hint?: string
  className?: string
}

const iconMap = {
  error: XCircle,
  warning: AlertCircle,
  info: Info,
  success: CheckCircle,
}

const colorMap = {
  error: {
    text: 'text-destructive',
    bg: 'bg-destructive/10',
    border: 'border-destructive/20',
    icon: 'text-destructive'
  },
  warning: {
    text: 'text-yellow-600 dark:text-yellow-400',
    bg: 'bg-yellow-50 dark:bg-yellow-900/20',
    border: 'border-yellow-200 dark:border-yellow-800',
    icon: 'text-yellow-600 dark:text-yellow-400'
  },
  info: {
    text: 'text-blue-600 dark:text-blue-400',
    bg: 'bg-blue-50 dark:bg-blue-900/20',
    border: 'border-blue-200 dark:border-blue-800',
    icon: 'text-blue-600 dark:text-blue-400'
  },
  success: {
    text: 'text-green-600 dark:text-green-400',
    bg: 'bg-green-50 dark:bg-green-900/20',
    border: 'border-green-200 dark:border-green-800',
    icon: 'text-green-600 dark:text-green-400'
  }
}

export function FormError({ 
  type = 'error', 
  message, 
  hint, 
  className 
}: FormErrorProps) {
  const Icon = iconMap[type]
  const colors = colorMap[type]

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, y: -10, height: 0 }}
        animate={{ opacity: 1, y: 0, height: 'auto' }}
        exit={{ opacity: 0, y: -10, height: 0 }}
        transition={{ duration: 0.2 }}
        className={cn(
          "rounded-lg border p-3 space-y-1",
          colors.bg,
          colors.border,
          className
        )}
      >
        <div className="flex items-start gap-2">
          <Icon className={cn("h-4 w-4 mt-0.5 flex-shrink-0", colors.icon)} />
          <div className="flex-1 space-y-1">
            <p className={cn("text-sm font-medium", colors.text)}>
              {message}
            </p>
            {hint && (
              <p className={cn("text-xs opacity-80", colors.text)}>
                {hint}
              </p>
            )}
          </div>
        </div>
      </motion.div>
    </AnimatePresence>
  )
}

// Predefined error messages for common scenarios
export const AUTH_ERRORS = {
  // Login errors
  INCORRECT_PASSWORD: {
    message: "Password doesn't match. Try again or reset it",
    hint: "Tip: Check for typos or caps lock!"
  },
  EMAIL_NOT_FOUND: {
    message: "We can't find an account with that email. New here? Sign up instead.",
    hint: "Double-check your email address"
  },
  EMPTY_EMAIL: {
    message: "Please enter your email to continue.",
    hint: "Your email address is required"
  },
  EMPTY_PASSWORD: {
    message: "Please enter your password to continue.",
    hint: "Your password is required"
  },
  TOO_MANY_ATTEMPTS: {
    message: "Too many failed attempts. Please wait 5 minutes or reset your password.",
    hint: "This helps protect your account"
  },
  CAPTCHA_FAIL: {
    message: "Please complete the CAPTCHA to confirm you're human.",
    hint: "This helps us prevent automated attacks"
  },

  // Signup errors
  EMAIL_EXISTS: {
    message: "This email is already registered. Try logging in or use a different email.",
    hint: "You might have signed up before"
  },
  WEAK_PASSWORD: {
    message: "Password too weak. Use at least 8 characters with numbers and symbols.",
    hint: "Strong passwords help protect your account"
  },
  EMPTY_FIELD: {
    message: "This field can't be blank.",
    hint: "All required fields must be filled"
  },
  INVALID_EMAIL: {
    message: "Please enter a valid email (e.g., <EMAIL>).",
    hint: "Check for typos in your email address"
  },
  PASSWORDS_DONT_MATCH: {
    message: "Passwords don't match. Re-enter them carefully.",
    hint: "Make sure both password fields are identical"
  },
  USERNAME_TAKEN: {
    message: "Username already taken. Try a different one.",
    hint: "Usernames must be unique"
  },
  AGE_RESTRICTION: {
    message: "You must be at least 18 to sign up.",
    hint: "This is required by our terms of service"
  }
}

// Helper component for displaying auth errors
export function AuthError({ 
  errorKey, 
  customMessage, 
  customHint,
  ...props 
}: {
  errorKey?: keyof typeof AUTH_ERRORS
  customMessage?: string
  customHint?: string
} & Omit<FormErrorProps, 'message' | 'hint'>) {
  const error = errorKey ? AUTH_ERRORS[errorKey] : null
  
  return (
    <FormError
      message={customMessage || error?.message || 'An error occurred'}
      hint={customHint || error?.hint}
      {...props}
    />
  )
}
