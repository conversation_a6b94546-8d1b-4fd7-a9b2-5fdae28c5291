// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                String    @id @default(cuid())
  email             String    @unique
  name              String?
  hashedPassword    String
  role              Role      @default(CLIENT)
  avatar            String?
  company           String?
  phone             String?
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt
  resetToken        String?
  resetTokenExpiry  DateTime?
  emailVerified     Boolean   @default(false)
  emailVerificationToken String?
  emailVerificationExpiry DateTime?

  // Questionnaire data
  servicesInterested String[]  // Array of services: web_dev, app_dev, ai_integration, collaboration
  projectSpecifications String? // Text field for project specifications
  hasCompletedQuestionnaire <PERSON>olean @default(false)

  // Login attempt tracking
  loginAttempts     Int       @default(0)
  lockedUntil       DateTime?

  // Relations
  projects          Project[]
  messages          Message[]
  notifications     Notification[]
  activities        Activity[]
}

model Project {
  id              String        @id @default(cuid())
  name            String
  description     String
  status          ProjectStatus @default(PLANNING)
  progress        Int           @default(0) // 0-100 percentage
  budget          Float?
  startDate       DateTime?
  expectedEndDate DateTime?
  actualEndDate   DateTime?
  previewUrl      String?       // URL for client to preview the website
  repositoryUrl   String?       // GitHub repository URL
  clientId        String
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt

  // Relations
  client          User          @relation(fields: [clientId], references: [id])
  updates         ProjectUpdate[]
  files           ProjectFile[]
  messages        Message[]
  collaborators   ProjectCollaborator[]
}

model ProjectUpdate {
  id          String   @id @default(cuid())
  title       String
  description String
  progress    Int      // 0-100 percentage
  projectId   String
  createdAt   DateTime @default(now())

  // Relations
  project     Project  @relation(fields: [projectId], references: [id], onDelete: Cascade)
}

model ProjectFile {
  id          String   @id @default(cuid())
  name        String
  url         String
  type        FileType
  size        Int      // File size in bytes
  projectId   String
  uploadedAt  DateTime @default(now())

  // Relations
  project     Project  @relation(fields: [projectId], references: [id], onDelete: Cascade)
}

model ProjectCollaborator {
  id        String   @id @default(cuid())
  userId    String
  projectId String
  role      CollaboratorRole @default(VIEWER)
  joinedAt  DateTime @default(now())

  // Relations
  user      User     @relation(fields: [userId], references: [id])
  project   Project  @relation(fields: [projectId], references: [id], onDelete: Cascade)

  @@unique([userId, projectId])
}

model Message {
  id          String      @id @default(cuid())
  content     String
  senderId    String
  receiverId  String?     // null for broadcast messages
  projectId   String?     // null for general messages
  type        MessageType @default(DIRECT)
  isRead      Boolean     @default(false)
  createdAt   DateTime    @default(now())

  // Relations
  sender      User        @relation(fields: [senderId], references: [id])
  project     Project?    @relation(fields: [projectId], references: [id], onDelete: Cascade)
}

model Notification {
  id        String           @id @default(cuid())
  title     String
  message   String
  type      NotificationType
  userId    String
  isRead    Boolean          @default(false)
  createdAt DateTime         @default(now())

  // Relations
  user      User             @relation(fields: [userId], references: [id])
}

model Activity {
  id          String       @id @default(cuid())
  action      String       // e.g., "created project", "updated status"
  description String
  userId      String
  entityType  String?      // e.g., "project", "user"
  entityId    String?      // ID of the related entity
  createdAt   DateTime     @default(now())

  // Relations
  user        User         @relation(fields: [userId], references: [id])
}

// Enums
enum Role {
  CLIENT
  ADMIN
  DEVELOPER
}

enum ProjectStatus {
  PLANNING
  IN_PROGRESS
  REVIEW
  TESTING
  COMPLETED
  ON_HOLD
  CANCELLED
}

enum FileType {
  IMAGE
  DOCUMENT
  CODE
  DESIGN
  OTHER
}

enum CollaboratorRole {
  VIEWER
  COLLABORATOR
  MANAGER
}

enum MessageType {
  DIRECT
  PROJECT
  BROADCAST
  SYSTEM
}

enum NotificationType {
  PROJECT_UPDATE
  MESSAGE
  SYSTEM
  REMINDER
}
