import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { verifyToken } from '@/lib/auth'
import { z } from 'zod'

const questionnaireSchema = z.object({
  servicesInterested: z.array(z.string()).min(1, 'Please select at least one service'),
  projectSpecifications: z.string().min(10, 'Please provide more details about your project'),
  timeline: z.string().optional(),
  budget: z.string().optional(),
})

export async function POST(request: Request) {
  try {
    const body = await request.json()
    const validatedData = questionnaireSchema.parse(body)

    // Get token from Authorization header
    const authHeader = request.headers.get('authorization')
    const token = authHeader?.replace('Bearer ', '') || 
                  request.headers.get('x-auth-token') ||
                  // For development, try to get from localStorage (this would be sent in the request)
                  body.token

    if (!token) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Verify token and get user
    const user = await verifyToken(token)
    if (!user) {
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 401 }
      )
    }

    // Update user with questionnaire data
    const updatedUser = await prisma.user.update({
      where: { id: user.id },
      data: {
        servicesInterested: validatedData.servicesInterested,
        projectSpecifications: validatedData.projectSpecifications,
        hasCompletedQuestionnaire: true,
      },
      select: {
        id: true,
        email: true,
        name: true,
        servicesInterested: true,
        projectSpecifications: true,
        hasCompletedQuestionnaire: true,
      },
    })

    // Create initial project based on questionnaire
    if (validatedData.servicesInterested.length > 0) {
      await prisma.project.create({
        data: {
          name: `${updatedUser.name || 'Client'}'s Project`,
          description: validatedData.projectSpecifications,
          status: 'PLANNING',
          clientId: user.id,
        },
      })
    }

    // Create welcome notification
    await prisma.notification.create({
      data: {
        title: 'Welcome to Your Dashboard!',
        message: 'Your profile has been completed successfully. We\'ll be in touch soon to discuss your project.',
        type: 'WELCOME',
        userId: user.id,
      },
    })

    return NextResponse.json({
      message: 'Questionnaire completed successfully',
      user: updatedUser,
    })
  } catch (error) {
    console.error('Questionnaire error:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.issues[0].message },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
