'use client'

import * as React from "react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { GlassCard } from "@/components/ui/glass-card"
import { AnimatedBackground } from "@/components/ui/animated-background"
import { FormError } from "@/components/ui/form-error"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { toast } from "sonner"
import { useRouter, useSearchParams } from "next/navigation"
import { Mail, CheckCircle, RefreshCw } from "lucide-react"
import { motion } from "framer-motion"

const verifyEmailSchema = z.object({
  code: z.string().min(6, "Verification code must be 6 digits").max(6, "Verification code must be 6 digits"),
})

type FormData = z.infer<typeof verifyEmailSchema>

export default function VerifyEmailPage() {
  const [isLoading, setIsLoading] = React.useState(false)
  const [isResending, setIsResending] = React.useState(false)
  const [isSuccess, setIsSuccess] = React.useState(false)
  const [countdown, setCountdown] = React.useState(0)
  
  const router = useRouter()
  const searchParams = useSearchParams()
  const email = searchParams.get('email') || ''
  
  const {
    register,
    handleSubmit,
    formState: { errors },
    watch
  } = useForm<FormData>({
    resolver: zodResolver(verifyEmailSchema),
  })

  const code = watch('code')

  // Countdown timer for resend button
  React.useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => setCountdown(countdown - 1), 1000)
      return () => clearTimeout(timer)
    }
  }, [countdown])

  const onSubmit = async (data: FormData) => {
    setIsLoading(true)
    try {
      const response = await fetch('/api/auth/verify-email', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          email,
          code: data.code
        }),
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Verification failed')
      }

      setIsSuccess(true)
      toast.success('Email verified successfully!')
      
      // Redirect to dashboard after a short delay
      setTimeout(() => {
        router.push('/dashboard/client-dashboard')
      }, 2000)
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Verification failed')
    } finally {
      setIsLoading(false)
    }
  }

  const handleResendCode = async () => {
    setIsResending(true)
    try {
      const response = await fetch('/api/auth/verify-email', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email }),
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Failed to resend code')
      }

      toast.success('Verification code sent!')
      setCountdown(60) // 60 second cooldown
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Failed to resend code')
    } finally {
      setIsResending(false)
    }
  }

  if (isSuccess) {
    return (
      <div className="relative flex min-h-screen flex-col items-center justify-center p-6 md:p-10 overflow-hidden">
        <AnimatedBackground variant="minimal" />
        <div className="relative z-10 w-full max-w-md">
          <GlassCard className="p-8 text-center space-y-6" blur="lg" opacity={0.9}>
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ type: "spring", duration: 0.6 }}
            >
              <CheckCircle className="w-16 h-16 text-green-500 mx-auto mb-4" />
            </motion.div>
            
            <div className="space-y-2">
              <h1 className="text-2xl font-bold">Email Verified!</h1>
              <p className="text-muted-foreground">
                Your email has been successfully verified. Redirecting to your dashboard...
              </p>
            </div>
          </GlassCard>
        </div>
      </div>
    )
  }

  return (
    <div className="relative flex min-h-screen flex-col items-center justify-center p-6 md:p-10 overflow-hidden">
      <AnimatedBackground variant="minimal" />
      <div className="relative z-10 w-full max-w-md">
        <GlassCard className="p-8 space-y-6" blur="lg" opacity={0.9}>
          <div className="space-y-2 text-center">
            <Mail className="w-12 h-12 text-primary mx-auto mb-4" />
            <h1 className="text-2xl font-bold">Verify your email</h1>
            <p className="text-muted-foreground">
              We've sent a 6-digit verification code to{" "}
              <span className="font-medium text-foreground">{email}</span>
            </p>
          </div>

          <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="code">Verification Code</Label>
              <Input
                id="code"
                type="text"
                placeholder="Enter 6-digit code"
                disabled={isLoading}
                {...register("code")}
                className="text-center text-lg tracking-widest"
                maxLength={6}
                autoComplete="one-time-code"
              />
              {errors.code && (
                <FormError 
                  type="error"
                  message={errors.code.message || "Invalid code"}
                />
              )}
            </div>

            <Button 
              type="submit" 
              disabled={isLoading || code?.length !== 6}
              className="w-full"
            >
              {isLoading ? "Verifying..." : "Verify Email"}
            </Button>
          </form>

          <div className="text-center space-y-3">
            <p className="text-sm text-muted-foreground">
              Didn't receive the code?
            </p>
            
            <Button
              type="button"
              variant="outline"
              disabled={isResending || countdown > 0}
              onClick={handleResendCode}
              className="w-full"
            >
              {isResending ? (
                <>
                  <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                  Sending...
                </>
              ) : countdown > 0 ? (
                `Resend in ${countdown}s`
              ) : (
                "Resend code"
              )}
            </Button>
          </div>
        </GlassCard>
      </div>
    </div>
  )
}
