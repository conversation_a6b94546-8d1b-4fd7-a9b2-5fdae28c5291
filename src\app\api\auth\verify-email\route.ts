import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const verifyEmailSchema = z.object({
  email: z.string().email('Invalid email address'),
  code: z.string().min(6, 'Verification code must be 6 digits').max(6, 'Verification code must be 6 digits'),
})

export async function POST(request: Request) {
  try {
    const body = await request.json()
    const validatedData = verifyEmailSchema.parse(body)

    // Find user with matching email and verification code
    const user = await prisma.user.findFirst({
      where: {
        email: validatedData.email,
        emailVerificationToken: validatedData.code,
        emailVerificationExpiry: {
          gt: new Date()
        }
      }
    })

    if (!user) {
      return NextResponse.json(
        { error: 'Invalid or expired verification code' },
        { status: 400 }
      )
    }

    // Update user as verified and clear verification token
    await prisma.user.update({
      where: { id: user.id },
      data: {
        emailVerified: true,
        emailVerificationToken: null,
        emailVerificationExpiry: null
      }
    })

    return NextResponse.json({
      message: 'Email verified successfully'
    })
  } catch (error) {
    console.error('Email verification error:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.issues[0].message },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Send verification code
export async function PUT(request: Request) {
  try {
    const body = await request.json()
    const { email } = z.object({
      email: z.string().email('Invalid email address')
    }).parse(body)

    // Find user
    const user = await prisma.user.findUnique({
      where: { email }
    })

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    if (user.emailVerified) {
      return NextResponse.json(
        { error: 'Email is already verified' },
        { status: 400 }
      )
    }

    // Generate 6-digit verification code
    const verificationCode = Math.floor(100000 + Math.random() * 900000).toString()
    
    // Update user with verification code (expires in 10 minutes)
    await prisma.user.update({
      where: { id: user.id },
      data: {
        emailVerificationToken: verificationCode,
        emailVerificationExpiry: new Date(Date.now() + 10 * 60 * 1000) // 10 minutes
      }
    })

    // TODO: In a real application, send this code via email
    // For development, we'll return it in the response
    return NextResponse.json({
      message: 'Verification code sent to your email',
      // Remove this in production:
      debug: {
        verificationCode
      }
    })
  } catch (error) {
    console.error('Send verification code error:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.issues[0].message },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
